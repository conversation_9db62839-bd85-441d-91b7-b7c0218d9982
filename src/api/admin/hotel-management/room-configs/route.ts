import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";
import { z } from "zod";
import {
  PostAdminCreateRoomConfig,
  PostAdminDeleteRoomConfig,
  PostAdminUpdateRoomConfig,
} from "./validators";
import linkPriceSetToRoomConfigWorkflow from "src/workflows/hotel-management/room/link-price-set-to-room-config";

type PostAdminCreateRoomConfigType = z.infer<typeof PostAdminCreateRoomConfig>;
type PostAdminUpdateRoomConfigType = z.infer<typeof PostAdminUpdateRoomConfig>;
type PostAdminDeleteRoomConfigType = z.infer<typeof PostAdminDeleteRoomConfig>;

// POST endpoint to create a room configuration
export const POST = async (
  req: MedusaRequest<PostAdminCreateRoomConfigType>,
  res: MedusaResponse
) => {
  try {
    console.log("=== POST /admin/hotel-management/room-configs ====");
    console.log("Request body:", req.body);

    const {
      name,
      type,
      description,
      room_size,
      bed_type,
      max_extra_beds,
      max_adults,
      max_children,
      max_infants,
      max_occupancy,
      amenities,
      hotel_id,
    } = req.body;

    if (!hotel_id) {
      throw new Error("hotel_id is required");
    }

    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    console.log("Creating room configuration with hotel_id:", hotel_id);

    // Create a product to represent the room configuration
    let product;
    try {
      // Ensure all values are properly formatted
      const metadata = {
        type: type || "standard",
        room_size: room_size || "",
        bed_type: bed_type || "",
        max_extra_beds: max_extra_beds || 0,
        max_adults: max_adults || 1,
        max_children: max_children || 0,
        max_infants: max_infants || 0,
        max_occupancy: max_occupancy || 1,
        amenities: amenities || [],
        hotel_id: String(hotel_id), // Ensure hotel_id is stored as a string
      };

      // Create the product data with the correct types
      const productData = {
        title: name,
        description: description || "",
        is_giftcard: false,
        status: "published" as const, // Use as const to ensure correct type
        metadata,
        // Don't set categories here to avoid the error
        handle: `room-config-${Date.now()}`, // Add a unique handle
      };

      console.log(
        "Creating product with data:",
        JSON.stringify(productData, null, 2)
      );
      // Make sure we're passing the correct format to createProducts
      product = await productModuleService.createProducts(productData);

      console.log("Created product:", {
        id: product.id,
        title: product.title,
        metadata: product.metadata,
      });

      // Double-check that the hotel_id was saved correctly
      if (product.metadata?.hotel_id !== String(hotel_id)) {
        console.error("hotel_id was not saved correctly in metadata");
        console.error("Expected:", String(hotel_id));
        console.error("Actual:", product.metadata?.hotel_id);
      }
    } catch (error) {
      console.error("Error creating product:", error);
      throw new Error(`Failed to create room configuration: ${error.message}`);
    }

    // Automatically create and link a price set for the new room configuration
    let priceSetId = null;
    try {
      console.log(`Creating price set for room configuration: ${product.id}`);
      const { result } = await linkPriceSetToRoomConfigWorkflow(req.scope).run({
        input: {
          room_config_id: product.id,
          currency_code: "USD", // Default currency
          base_price: 10000, // Default $100.00
        },
      });
      priceSetId = result.price_set_id;
      console.log(`Successfully created and linked price set: ${priceSetId}`);
    } catch (error) {
      console.warn(
        `Failed to create price set for room config ${product.id}:`,
        error
      );
      // Don't fail the room config creation if price set creation fails
    }

    // Transform the product to a room configuration
    const roomConfig = {
      id: product.id,
      name: product.title,
      type: product.metadata?.type || "standard",
      description: product.description,
      room_size: product.metadata?.room_size,
      bed_type: product.metadata?.bed_type,
      max_extra_beds: product.metadata?.max_extra_beds || 0,
      max_adults: product.metadata?.max_adults || 1,
      max_children: product.metadata?.max_children || 0,
      max_infants: product.metadata?.max_infants || 0,
      max_occupancy: product.metadata?.max_occupancy || 1,
      amenities: product.metadata?.amenities || [],
      hotel_id: hotel_id,
      price_set_id: priceSetId, // Include the price set ID in the response
    };

    res.json({ roomConfig });
  } catch (error) {
    console.error("Error creating room configuration:", error);
    res.status(400).json({
      message:
        error instanceof Error
          ? error.message
          : "Failed to create room configuration",
    });
  }
};

// PUT endpoint to update a room configuration
export const PUT = async (
  req: MedusaRequest<PostAdminUpdateRoomConfigType>,
  res: MedusaResponse
) => {
  try {
    console.log("=== PUT /admin/hotel-management/room-configs ====");
    const { id, ...updateData } = req.body;
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Get the product that represents the room configuration
    const product = await productModuleService.retrieveProduct(id, {
      relations: ["categories"],
    });

    if (!product) {
      return res.status(404).json({ message: "Room configuration not found" });
    }

    // Update the product
    const updatedProduct = await productModuleService.updateProducts(id, {
      title: updateData.name,
      description: updateData.description,
      metadata: {
        ...product.metadata,
        type: updateData.type,
        room_size: updateData.room_size,
        bed_type: updateData.bed_type,
        max_extra_beds: updateData.max_extra_beds,
        max_adults: updateData.max_adults,
        max_children: updateData.max_children,
        max_infants: updateData.max_infants,
        max_occupancy: updateData.max_occupancy,
        amenities: updateData.amenities,
        // Keep the hotel_id in metadata
        hotel_id: updateData.hotel_id || product.metadata?.hotel_id,
      },
    });

    // Transform the product to a room configuration
    const roomConfig = {
      id: updatedProduct.id,
      name: updatedProduct.title,
      type: updatedProduct.metadata?.type || "standard",
      description: updatedProduct.description,
      room_size: updatedProduct.metadata?.room_size,
      bed_type: updatedProduct.metadata?.bed_type,
      max_extra_beds: updatedProduct.metadata?.max_extra_beds || 0,
      max_adults: updatedProduct.metadata?.max_adults || 1,
      max_children: updatedProduct.metadata?.max_children || 0,
      max_infants: updatedProduct.metadata?.max_infants || 0,
      max_occupancy: updatedProduct.metadata?.max_occupancy || 1,
      amenities: updatedProduct.metadata?.amenities || [],
      hotel_id: updatedProduct.metadata?.hotel_id,
    };

    res.json({ roomConfig });
  } catch (error) {
    console.error("Error updating room configuration:", error);
    res.status(400).json({
      message:
        error instanceof Error
          ? error.message
          : "Failed to update room configuration",
    });
  }
};

// DELETE endpoint to delete a room configuration
export const DELETE = async (
  req: MedusaRequest<PostAdminDeleteRoomConfigType>,
  res: MedusaResponse
) => {
  try {
    console.log("=== DELETE /admin/hotel-management/room-configs ====");
    const { ids } = req.body;
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Convert single ID to array if needed
    const idArray = Array.isArray(ids) ? ids : [ids];
    const results = [];

    // Delete each product that represents a room configuration
    for (const id of idArray) {
      try {
        // Delete the product
        await productModuleService.deleteProducts(id);
        results.push({ id, deleted: true });
      } catch (error) {
        console.error(`Error deleting room configuration ${id}:`, error);
        results.push({ id, deleted: false, error: error.message });
      }
    }

    res.json({ roomConfig: results });
  } catch (error) {
    console.error("Error deleting room configuration:", error);
    res.status(400).json({
      message:
        error instanceof Error
          ? error.message
          : "Failed to delete room configuration",
    });
  }
};

// GET endpoint to list room configurations
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    console.log("=== GET /admin/hotel-management/room-configs ====");
    const { hotel_id } = req.query;
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Get all products
    let products = [];
    try {
      console.log("Fetching all products...");
      const result = await productModuleService.listProducts(
        {
          is_giftcard: false,
        },
        {
          relations: ["categories"],
        }
      );

      // Handle the result based on the return type
      if (Array.isArray(result)) {
        products = result;
      } else if (result && typeof result === "object") {
        // Use type assertion to handle the case where result has a products property
        const resultWithProducts = result as { products?: any[] };
        if (resultWithProducts.products) {
          products = resultWithProducts.products;
        }
      }
      console.log(`Found ${products.length} total products`);
    } catch (error) {
      console.error("Error listing products:", error);
      products = [];
    }

    // Manually filter products by metadata.hotel_id
    console.log(`Filtering products for hotel_id: ${hotel_id}`);
    const filteredProducts = products.filter((product) => {
      try {
        // Check if product has metadata
        if (!product.metadata) {
          return false;
        }

        // Convert both to strings for comparison to avoid type mismatches
        const productHotelId = String(product.metadata.hotel_id || "");
        const queryHotelId = String(hotel_id || "");

        return productHotelId === queryHotelId;
      } catch (error) {
        console.error(`Error filtering product ${product.id}:`, error);
        return false;
      }
    });

    console.log(
      `After filtering, found ${filteredProducts.length} products for hotel_id: ${hotel_id}`
    );

    // Transform products to room configurations
    const roomConfigs = filteredProducts.map((product) => ({
      id: product.id,
      name: product.title,
      type: product.metadata?.type || "standard",
      description: product.description,
      room_size: product.metadata?.room_size,
      bed_type: product.metadata?.bed_type,
      max_extra_beds: product.metadata?.max_extra_beds || 0,
      max_adults: product.metadata?.max_adults || 1,
      max_children: product.metadata?.max_children || 0,
      max_infants: product.metadata?.max_infants || 0,
      max_occupancy: product.metadata?.max_occupancy || 1,
      amenities: product.metadata?.amenities || [],
      hotel_id: product.metadata?.hotel_id,
    }));

    res.json({
      roomConfigs,
      count: filteredProducts.length,
      limit: filteredProducts.length,
      offset: 0,
    });
  } catch (error) {
    console.error("Error fetching room configurations:", error);
    res.status(400).json({
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch room configurations",
    });
  }
};
