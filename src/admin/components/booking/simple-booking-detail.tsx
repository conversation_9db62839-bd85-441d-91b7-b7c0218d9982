import { useState, useEffect } from "react";
import {
  Button,
  Heading,
  Text,
  Toaster,
  toast,
  FocusModal,
  Textarea,
} from "@camped-ai/ui";
import { format } from "date-fns";
import { useNavigate } from "react-router-dom";
import Spinner from "../../components/shared/spinner";
import PaymentLinkButton from "./payment-link-button";
import WhatsAppMessagesPanel from "./whatsapp-messages-panel";

// Status badge colors
const statusColors: Record<string, string> = {
  pending: "yellow",
  confirmed: "green",
  checked_in: "blue",
  checked_out: "purple",
  canceled: "gray",
  no_show: "gray",
};

// Payment status badge colors
const paymentStatusColors: Record<string, string> = {
  not_paid: "red",
  awaiting_payment: "yellow",
  partially_paid: "orange",
  paid: "green",
  refunded: "purple",
  partially_refunded: "blue",
};

interface SimpleBookingDetailProps {
  bookingId: string | undefined;
  isInSidebar?: boolean;
}

const SimpleBookingDetail = ({
  bookingId,
  isInSidebar = false,
}: SimpleBookingDetailProps) => {
  const navigate = useNavigate();
  const [booking, setBooking] = useState<any>(null);
  const [hotelDetails, setHotelDetails] = useState<any>(null);
  const [roomDetails, setRoomDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDownloadingInvoice, setIsDownloadingInvoice] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [cancellationReason, setCancellationReason] = useState("");

  // Function to handle invoice download
  const handleDownloadInvoice = async () => {
    if (!bookingId) {
      toast.error("Error", {
        description: "Booking ID is missing.",
      });
      return;
    }

    setIsDownloadingInvoice(true);
    try {
      // Endpoint should match your backend route
      const response = await fetch(
        `/admin/hotel-management/bookings/${bookingId}/invoice`
      );

      if (!response.ok) {
        // Try to parse error message from backend if available
        let errorMsg = `Failed to download invoice: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData && errorData.message) {
            errorMsg = errorData.message;
          }
        } catch (e) {
          // Ignore if response is not JSON
        }
        throw new Error(errorMsg);
      }

      const blob = await response.blob();
      if (blob.type !== "application/pdf") {
        // If the backend didn't send a PDF, it might be an error message in text/html or json
        const errorText = await blob.text();
        let parsedError =
          "An unexpected error occurred. The server did not return a PDF.";
        try {
          const jsonError = JSON.parse(errorText);
          if (jsonError.message) parsedError = jsonError.message;
        } catch (e) {
          // If not JSON, use the raw text if it's short, or a generic message
          if (errorText.length < 200) parsedError = errorText;
        }
        throw new Error(parsedError);
      }

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      // Use booking.id or booking.order_id from the booking state object
      a.download = `invoice-${
        booking?.display_id || booking?.id || booking?.order_id || bookingId
      }.pdf`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);

      toast.success("Success", {
        description: "Invoice download started.",
      });
    } catch (error: any) {
      console.error("Error downloading invoice:", error);
      toast.error("Download Failed", {
        description: error.message || "Could not download invoice.",
      });
    } finally {
      setIsDownloadingInvoice(false);
    }
  };

  // Fetch booking details
  const fetchBookingDetails = async () => {
    if (!bookingId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      const response = await fetch(
        `/admin/hotel-management/bookings/${bookingId}`
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch booking details: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data.booking) {
        throw new Error("No booking data in response");
      }

      // Process booking data
      const processedBooking = { ...data.booking };

      // Parse metadata if it's a string
      if (typeof processedBooking.metadata === "string") {
        try {
          processedBooking.metadata = JSON.parse(processedBooking.metadata);
        } catch (e) {
          console.error("Failed to parse metadata string:", e);
        }
      }

      // Debug total amount
      console.log("Total amount sources:", {
        total_amount: processedBooking.total_amount,
        total: processedBooking.total,
        metadata_total_amount: processedBooking.metadata?.total_amount,
        metadata_total: processedBooking.metadata?.total,
        items: processedBooking.items,
        item_price:
          processedBooking.items && processedBooking.items[0]?.unit_price,
        item_quantity:
          processedBooking.items && processedBooking.items[0]?.quantity,
      });

      // If total amount is missing, try to calculate it
      if (
        !processedBooking.total_amount &&
        processedBooking.items &&
        processedBooking.items.length > 0
      ) {
        const item = processedBooking.items[0];
        if (item.unit_price && item.quantity) {
          processedBooking.total_amount = item.unit_price * item.quantity;
          console.log(
            "Calculated total amount:",
            processedBooking.total_amount
          );
        }
      }

      setBooking(processedBooking);
      console.log("Booking state set:", processedBooking);

      // Fetch hotel details if we have a hotel ID
      if (processedBooking.hotel_id || processedBooking.metadata?.hotel_id) {
        fetchHotelDetails(
          processedBooking.hotel_id || processedBooking.metadata?.hotel_id
        );
      }

      // Fetch room details if we have a room config ID
      if (
        processedBooking.room_config_id ||
        processedBooking.product_id ||
        processedBooking.metadata?.room_config_id ||
        processedBooking.metadata?.room_id
      ) {
        fetchRoomDetails(
          processedBooking.room_config_id ||
            processedBooking.product_id ||
            processedBooking.metadata?.room_config_id ||
            processedBooking.metadata?.room_id
        );
      }
    } catch (error: any) {
      console.error("Error fetching booking details:", error);
      toast.error("Error", {
        description: `Failed to fetch booking details: ${error.message}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch hotel details
  const fetchHotelDetails = async (hotelId: string) => {
    try {
      console.log(`Fetching hotel details for ID: ${hotelId}`);
      const response = await fetch(`/admin/hotel-management/hotels/${hotelId}`);

      if (!response.ok) {
        console.error(`Failed to fetch hotel details: ${response.status}`);
        return;
      }

      const data = await response.json();

      if (data.hotel) {
        setHotelDetails(data.hotel);
      } else if (data.hotels && data.hotels.length > 0) {
        setHotelDetails(data.hotels[0]);
      } else {
        console.error("No hotel data found in response");
      }
    } catch (error) {
      console.error("Error fetching hotel details:", error);
    }
  };

  // Function to fetch room details
  const fetchRoomDetails = async (roomId: string) => {
    try {
      console.log(`Fetching room details for ID: ${roomId}`);

      // Try different endpoints to get room details
      let response = await fetch(`/admin/products/${roomId}`);

      if (response.ok) {
        const data = await response.json();
        if (data.product) {
          setRoomDetails(data.product);
          return;
        }
      }

      // Try direct-room-configs endpoint
      response = await fetch(`/admin/direct-room-configs/${roomId}`);

      if (response.ok) {
        const data = await response.json();
        if (data.product) {
          setRoomDetails(data.product);
          return;
        }
      }

      // Try variants endpoint
      response = await fetch(`/admin/products/variants/${roomId}`);

      if (response.ok) {
        const data = await response.json();
        if (data.variant && data.variant.product_id) {
          const productResponse = await fetch(
            `/admin/direct-room-configs/${data.variant.product_id}`
          );

          if (productResponse.ok) {
            const productData = await productResponse.json();
            if (productData.product) {
              productData.product.variant = data.variant;
              setRoomDetails(productData.product);
              return;
            }
          }
        }
      }

      console.error(`Could not fetch room details for ID: ${roomId}`);
    } catch (error) {
      console.error("Error fetching room details:", error);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchBookingDetails();
  }, [bookingId]);

  // Cancel booking
  const handleCancelBooking = async () => {
    if (!bookingId) {
      toast.error("Error", {
        description: "No booking ID provided",
      });
      return;
    }

    try {
      const response = await fetch(
        `/admin/hotel-management/bookings/${bookingId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            reason: cancellationReason,
            cancelled_by: "admin",
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to cancel booking");
      }

      const data = await response.json();
      setBooking(data.booking);

      toast.success("Success", {
        description: "Booking cancelled successfully",
      });

      // Check if we need to refresh availability data
      if (data.refresh_availability) {
        // Dispatch an event to notify that availability data should be refreshed
        const event = new CustomEvent("booking_cancelled", {
          detail: {
            bookingId: bookingId,
            roomId: booking?.room_id || booking?.metadata?.room_id,
            checkInDate:
              booking?.check_in_date || booking?.metadata?.check_in_date,
            checkOutDate:
              booking?.check_out_date || booking?.metadata?.check_out_date,
          },
        });
        window.dispatchEvent(event);
      }

      // Close dialog
      setIsCancelDialogOpen(false);
    } catch (error) {
      console.error("Error cancelling booking:", error);
      toast.error("Error", {
        description: "Failed to cancel booking",
      });
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd MMM yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Go back to bookings list or close sidebar
  const handleGoBack = () => {
    if (isInSidebar) {
      // If in sidebar, we'll handle this in the parent component
      // by setting selectedBookingId to null
      // The close button is now in the parent component, so this is not needed
    } else {
      // Otherwise, go back to the bookings list
      navigate("/hotel-management/bookings");
    }
  };

  // Handle payment link success
  const handlePaymentLinkSuccess = (paymentLink: any) => {
    // Update the booking object with the payment link information
    setBooking({
      ...booking,
      metadata: {
        ...booking.metadata,
        payment_link: paymentLink.url,
        payment_link_id: paymentLink.id,
        payment_link_expires_at: paymentLink.expires_at,
      },
    });
  };

  // Update traveler info
  const handleUpdateTravelerInfo = () => {
    if (!bookingId) return;
    navigate(`/hotel-management/bookings/${bookingId}/update-guest`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner size="medium" />
        <div className="ml-4">Loading booking details...</div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="text-center py-8">
        <Heading>Booking Not Found</Heading>
        <Text className="mt-2">
          The booking you're looking for doesn't exist or has been removed.
        </Text>
        <Button className="mt-4" onClick={handleGoBack}>
          Back to Bookings
        </Button>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${isInSidebar ? "p-4" : ""}`}>
      <Toaster />

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
          {!isInSidebar && (
            <div className="flex items-center gap-2 mb-2">
              <Button variant="secondary" onClick={handleGoBack} size="small">
                ← Back
              </Button>
              <Heading className="text-2xl">Booking Details</Heading>
            </div>
          )}
          <Text className="text-gray-500">
            Booking ID: {booking.id || booking.order_id}
          </Text>
        </div>

        <div className="flex flex-col gap-2 mt-4 sm:mt-0">
          <div className="flex items-center gap-2">
            <Text className="text-sm text-gray-500">Status:</Text>
            <div
              className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium capitalize bg-${
                statusColors[booking.status] || "gray"
              }-100 text-${statusColors[booking.status] || "gray"}-800`}
            >
              {booking.status?.replace("_", " ") || "Pending"}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Text className="text-sm text-gray-500">Payment:</Text>
            <div
              className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium capitalize bg-${
                paymentStatusColors[booking.payment_status] || "gray"
              }-100 text-${
                paymentStatusColors[booking.payment_status] || "gray"
              }-800`}
            >
              {booking.payment_status?.replace("_", " ") || "Not Paid"}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Layout depends on whether in sidebar or not */}
      <div
        className={
          isInSidebar ? "space-y-6" : "grid grid-cols-1 lg:grid-cols-3 gap-6"
        }
      >
        {/* Booking Details (full width in sidebar, 2/3 width on large screens otherwise) */}
        <div className={isInSidebar ? "space-y-6" : "lg:col-span-2 space-y-6"}>
          {/* Guest Information Card */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-lg font-medium mb-4">Guest Information</h3>

            <div className="space-y-4">
              <div>
                <Text className="text-sm text-gray-500">Guest Name</Text>
                <Text className="font-medium">
                  {booking.guest_name ||
                    booking.metadata?.guest_name ||
                    "Not provided"}
                </Text>
              </div>

              <div>
                <Text className="text-sm text-gray-500">Email</Text>
                <Text className="font-medium">
                  {booking.guest_email ||
                    booking.metadata?.guest_email ||
                    booking.email ||
                    "Not provided"}
                </Text>
              </div>

              <div>
                <Text className="text-sm text-gray-500">Phone</Text>
                <Text className="font-medium">
                  {booking.guest_phone ||
                    booking.metadata?.guest_phone ||
                    "Not provided"}
                </Text>
              </div>

              <div>
                <Text className="text-sm text-gray-500">Number of Guests</Text>
                <Text className="font-medium">
                  {booking.number_of_guests ||
                    booking.metadata?.number_of_guests ||
                    1}
                </Text>
              </div>
            </div>
          </div>

          {/* Booking Information Card */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mt-6">
            <h3 className="text-lg font-medium mb-4">Booking Information</h3>

            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:justify-between gap-4">
                <div>
                  <Text className="text-sm text-gray-500">
                    Check-in Date & Time
                  </Text>
                  <Text className="font-medium">
                    {formatDate(
                      booking.check_in_date || booking.metadata?.check_in_date
                    )}{" "}
                    {booking.check_in_time ||
                      booking.metadata?.check_in_time ||
                      "12:00"}
                  </Text>
                </div>

                <div>
                  <Text className="text-sm text-gray-500">
                    Check-out Date & Time
                  </Text>
                  <Text className="font-medium">
                    {formatDate(
                      booking.check_out_date || booking.metadata?.check_out_date
                    )}{" "}
                    {booking.check_out_time ||
                      booking.metadata?.check_out_time ||
                      "12:00"}
                  </Text>
                </div>
              </div>

              <div>
                <Text className="text-sm text-gray-500">Hotel</Text>
                <Text className="font-medium">
                  {hotelDetails?.name || booking.hotel_name || "Grand Hotel"}
                </Text>
                {!hotelDetails?.name &&
                  (booking.hotel_id || booking.metadata?.hotel_id) && (
                    <Text className="text-xs text-gray-400">
                      ID: {booking.hotel_id || booking.metadata?.hotel_id}
                    </Text>
                  )}
              </div>

              <div>
                <Text className="text-sm text-gray-500">Room Type</Text>
                <Text className="font-medium">
                  {roomDetails?.title ||
                    booking.room_type ||
                    booking.metadata?.room_type ||
                    "Standard Room"}
                </Text>
                {!roomDetails?.title &&
                  (booking.room_config_id ||
                    booking.metadata?.room_config_id ||
                    booking.metadata?.room_id) && (
                    <Text className="text-xs text-gray-400">
                      ID:{" "}
                      {booking.room_config_id ||
                        booking.metadata?.room_config_id ||
                        booking.metadata?.room_id}
                    </Text>
                  )}
              </div>

              <div>
                <Text className="text-sm text-gray-500">Number of Rooms</Text>
                <Text className="font-medium">
                  {booking.number_of_rooms ||
                    booking.metadata?.number_of_rooms ||
                    1}
                </Text>
              </div>

              {/* Pricing Breakdown */}
              <div className="space-y-2">
                <Text className="text-sm text-gray-500">Pricing Breakdown</Text>

                {/* Room Amount */}
                <div className="flex justify-between items-center">
                  <Text className="text-sm">Room Cost:</Text>
                  <Text className="font-medium">
                    {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: booking.currency_code || "USD",
                    }).format(
                      (booking.metadata?.total_amount ||
                        booking.total_amount ||
                        booking.total ||
                        (booking.items &&
                          booking.items[0]?.unit_price *
                            (booking.items[0]?.quantity || 1)) ||
                        0)
                    )}
                  </Text>
                </div>

                {/* Add-ons Amount (if exists) */}
                {booking.metadata?.add_on_total_amount &&
                  booking.metadata.add_on_total_amount > 0 && (
                    <div className="flex justify-between items-center">
                      <Text className="text-sm">Add-ons:</Text>
                      <Text className="font-medium">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: booking.currency_code || "USD",
                        }).format(
                          (booking.metadata.add_on_total_amount || 0)
                        )}
                      </Text>
                    </div>
                  )}

                {/* Total Amount */}
                <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                  <Text className="font-medium">Total Amount:</Text>
                  <Text className="font-semibold text-lg">
                    {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: booking.currency_code || "USD",
                    }).format(
                      ((booking.metadata?.total_amount ||
                        booking.total_amount ||
                        booking.total ||
                        (booking.items &&
                          booking.items[0]?.unit_price *
                            (booking.items[0]?.quantity || 1)) ||
                        0) +
                        (booking.metadata?.add_on_total_amount || 0))
                    )}
                  </Text>
                </div>
              </div>

              {/* Payment Link Information (if exists) */}
              {booking.metadata?.payment_link && (
                <div className="mt-2 p-3 bg-gray-50 rounded-md">
                  <div className="flex items-center justify-between">
                    <Text className="text-sm font-medium">Payment Link</Text>
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        navigator.clipboard.writeText(
                          booking.metadata.payment_link
                        );
                        toast.success("Copied", {
                          description: "Payment link copied to clipboard",
                        });
                      }}
                    >
                      Copy Link
                    </Button>
                  </div>
                  {booking.metadata.payment_link_expires_at && (
                    <Text className="text-xs text-gray-500 mt-1">
                      Expires:{" "}
                      {new Date(
                        booking.metadata.payment_link_expires_at
                      ).toLocaleString()}
                    </Text>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Add-ons Section */}
          {booking.metadata?.add_ons && booking.metadata.add_ons.length > 0 && (
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-medium mb-4">Add-on Services</h3>
              <div className="space-y-4">
                {booking.metadata.add_ons.map((addon: any, index: number) => (
                  <div
                    key={index}
                    className="border-b border-gray-100 pb-4 last:border-b-0 last:pb-0"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <Text className="font-medium text-base">
                          {addon.name}
                        </Text>
                        {addon.description && (
                          <Text className="text-sm text-gray-600 mt-1">
                            {addon.description}
                          </Text>
                        )}
                        <div className="flex gap-4 mt-2">
                          {addon.adult_quantity > 0 && (
                            <div className="text-sm">
                              <span className="text-gray-500">Adults:</span>{" "}
                              <span className="font-medium">
                                {addon.adult_quantity}
                              </span>
                              {addon.adult_price > 0 && (
                                <span className="text-gray-500 ml-1">
                                  @{" "}
                                  {new Intl.NumberFormat("en-US", {
                                    style: "currency",
                                    currency:
                                      addon.currency_code ||
                                      booking.currency_code ||
                                      "USD",
                                  }).format((addon.adult_price || 0))}
                                </span>
                              )}
                            </div>
                          )}
                          {addon.child_quantity > 0 && (
                            <div className="text-sm">
                              <span className="text-gray-500">Children:</span>{" "}
                              <span className="font-medium">
                                {addon.child_quantity}
                              </span>
                              {addon.child_price > 0 && (
                                <span className="text-gray-500 ml-1">
                                  @{" "}
                                  {new Intl.NumberFormat("en-US", {
                                    style: "currency",
                                    currency:
                                      addon.currency_code ||
                                      booking.currency_code ||
                                      "USD",
                                  }).format((addon.child_price || 0))}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                        {addon.service_level && (
                          <div className="mt-1">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {addon.service_level === "hotel"
                                ? "Hotel Service"
                                : "Destination Service"}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="text-right ml-4">
                        <Text className="font-semibold text-lg">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency:
                              addon.currency_code ||
                              booking.currency_code ||
                              "USD",
                          }).format((addon.total_price || 0))}
                        </Text>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Add-ons Total */}
                {booking.metadata.add_on_total_amount && (
                  <div className="border-t border-gray-200 pt-4 mt-4">
                    <div className="flex justify-between items-center">
                      <Text className="font-medium text-base">
                        Add-ons Total:
                      </Text>
                      <Text className="font-semibold text-lg">
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: booking.currency_code || "USD",
                        }).format(
                          (booking.metadata.add_on_total_amount || 0)
                        )}
                      </Text>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Special Requests Section (if any) */}
          {booking.special_requests && (
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-medium mb-2">Special Requests</h3>
              <Text>{booking.special_requests}</Text>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 justify-end mt-6">
            <PaymentLinkButton
              bookingId={bookingId || ""}
              onSuccess={handlePaymentLinkSuccess}
            />

            <Button variant="secondary" onClick={handleUpdateTravelerInfo}>
              Update Traveler Info
            </Button>

            <Button
              variant="secondary"
              onClick={handleDownloadInvoice}
              disabled={isDownloadingInvoice}
            >
              {isDownloadingInvoice ? "Downloading..." : "Download Invoice"}
            </Button>

            {booking.status !== "canceled" && (
              <>
                <Button
                  variant="danger"
                  onClick={() => setIsCancelDialogOpen(true)}
                >
                  Cancel Booking
                </Button>

                <FocusModal
                  open={isCancelDialogOpen}
                  onOpenChange={setIsCancelDialogOpen}
                >
                  <FocusModal.Content>
                    <FocusModal.Header>
                      <FocusModal.Title>Cancel Booking</FocusModal.Title>
                    </FocusModal.Header>
                    <FocusModal.Body className="p-6">
                      <Text className="mb-4">
                        Are you sure you want to cancel this booking? This
                        action cannot be undone.
                      </Text>
                      <div className="py-4">
                        <Text className="mb-2">Cancellation Reason</Text>
                        <Textarea
                          placeholder="Enter reason for cancellation"
                          value={cancellationReason}
                          onChange={(e) =>
                            setCancellationReason(e.target.value)
                          }
                        />
                      </div>
                    </FocusModal.Body>
                    <FocusModal.Footer>
                      <div className="flex justify-end gap-x-2">
                        <Button
                          variant="secondary"
                          onClick={() => setIsCancelDialogOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button variant="danger" onClick={handleCancelBooking}>
                          Confirm Cancellation
                        </Button>
                      </div>
                    </FocusModal.Footer>
                  </FocusModal.Content>
                </FocusModal>
              </>
            )}
          </div>
        </div>

        {/* Right Column - WhatsApp Messages (1/3 width on large screens, only if not in sidebar) */}
        {!isInSidebar && (
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm h-full">
              <WhatsAppMessagesPanel bookingId={bookingId} />
            </div>
          </div>
        )}

        {/* WhatsApp Messages (if in sidebar, show below booking details) */}
        {isInSidebar && (
          <div className="mt-6">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <WhatsAppMessagesPanel bookingId={bookingId} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleBookingDetail;
