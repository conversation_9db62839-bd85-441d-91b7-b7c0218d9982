# Add-ons API Usage Guide

This document explains how to use the new add-ons functionality in the hotel booking system.

## Overview

Add-ons are additional services that customers can select when making hotel bookings. They support:
- **Hotel-level** and **destination-level** add-ons
- **Per-person pricing** (separate adult and child pricing)
- **Quantity selection** for both adults and children
- **Capacity limits** and availability validation
- **Integration with cart and order flow**

## API Endpoints

### 1. Discover Available Add-ons

#### Get Hotel Add-ons
```http
GET /store/hotel-management/hotels/{hotel_id}/add-ons
```

**Query Parameters:**
- `currency_code` (optional): Currency for pricing (default: "USD")
- `is_active` (optional): Filter active add-ons (default: true)
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset (default: 0)

**Response:**
```json
{
  "add_ons": [
    {
      "id": "addon_service_123",
      "name": "Spa Treatment",
      "description": "Relaxing spa treatment",
      "service_type": "wellness",
      "service_level": "hotel",
      "adult_price": 5000,
      "child_price": 2500,
      "currency_code": "USD",
      "max_capacity": 10,
      "images": ["https://example.com/spa.jpg"],
      "hotel_id": "hotel_456",
      "hotel_name": "Mountain Resort"
    }
  ],
  "count": 1,
  "hotel": {
    "id": "hotel_456",
    "name": "Mountain Resort",
    "destination_id": "dest_789"
  }
}
```

#### Get Destination Add-ons
```http
GET /store/hotel-management/destinations/{destination_id}/add-ons
```

Similar response format but only includes destination-level add-ons.

### 2. Create Cart with Add-ons

Use the existing cart creation endpoint with add-ons included:

```http
POST /store/hotel-management/cart
```

**Request Body:**
```json
{
  "hotel_id": "hotel_456",
  "room_config_id": "room_cfg_123",
  "check_in_date": "2024-01-15",
  "check_out_date": "2024-01-20",
  "guest_name": "John Doe",
  "guest_email": "<EMAIL>",
  "adults": 2,
  "children": 1,
  "number_of_rooms": 1,
  "total_amount": 50000,
  "currency_code": "USD",
  "region_id": "reg_01JP9R0NP6B5DXGDYHFSSW0FK1",
  "add_ons": [
    {
      "service_id": "addon_service_123",
      "adult_quantity": 2,
      "child_quantity": 1
    },
    {
      "service_id": "addon_service_456",
      "adult_quantity": 1,
      "child_quantity": 0
    }
  ]
}
```

**Response:**
The cart will include:
- Room line items
- Add-on line items (separate for adult/child pricing)
- Metadata with add-on details
- Total pricing including add-ons

## Frontend Integration Example

```javascript
// 1. Fetch available add-ons for a hotel
const fetchAddOns = async (hotelId) => {
  const response = await fetch(
    `${API_BASE}/store/hotel-management/hotels/${hotelId}/add-ons`,
    {
      headers: getHeaders(),
    }
  );
  return response.json();
};

// 2. Create cart with selected add-ons
const createCartWithAddOns = async (bookingData, selectedAddOns) => {
  const params = {
    ...bookingData,
    add_ons: selectedAddOns.map(addon => ({
      service_id: addon.id,
      adult_quantity: addon.adultQuantity,
      child_quantity: addon.childQuantity
    }))
  };

  const response = await fetch(
    `${API_BASE}/store/hotel-management/cart`,
    {
      method: "POST",
      headers: getHeaders(),
      body: JSON.stringify(params),
    }
  );
  return response.json();
};

// 3. Example usage
const selectedAddOns = [
  {
    id: "addon_service_123",
    name: "Spa Treatment",
    adultQuantity: 2,
    childQuantity: 1,
    adultPrice: 5000,
    childPrice: 2500
  }
];

const cart = await createCartWithAddOns(bookingData, selectedAddOns);
```

## Validation Rules

1. **Add-on Availability**: Add-ons must be available for the specific hotel or its destination
2. **Active Status**: Only active add-ons can be selected
3. **Capacity Limits**: Total quantity cannot exceed max_capacity
4. **Quantity Requirements**: At least one adult or child quantity must be > 0
5. **Service Validation**: Add-on service must exist and be valid

## Error Handling

Common error responses:

```json
{
  "message": "Add-on service \"Spa Treatment\" is not available for this hotel"
}
```

```json
{
  "message": "Add-on service \"Spa Treatment\" exceeds maximum capacity of 10"
}
```

```json
{
  "message": "Invalid add-on service: addon_service_123",
  "error": "Product not found"
}
```

## Data Flow

1. **Discovery**: Customer views available add-ons for hotel
2. **Selection**: Customer selects add-ons with quantities
3. **Validation**: System validates add-on availability and constraints
4. **Cart Creation**: Room + add-ons added as separate line items
5. **Metadata Storage**: Add-on details stored in cart metadata
6. **Checkout**: Standard cart completion flow includes add-ons
7. **Order**: Final order includes all line items and metadata

## Notes

- Add-ons don't require inventory management (uses capacity limits instead)
- Pricing is stored in cents (multiply by 100 for API calls)
- Add-ons are stored as products with special metadata
- Both hotel-level and destination-level add-ons appear for hotel bookings
- Add-on line items are separate from room line items for clear pricing breakdown
